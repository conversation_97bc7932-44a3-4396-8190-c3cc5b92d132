# Discord-like Role and Permissions System Implementation Plan

## Overview
This plan outlines the implementation of a comprehensive role and permissions system similar to <PERSON>rd's, featuring hierarchical roles, granular permissions, channel-specific overwrites, and a complete management UI.

## Current System Analysis
- **Existing**: Basic permission system with Users, Agents, and Permissions tables
- **Current Permission Levels**: Read, Write, Admin (simple 3-level system)
- **Current Task Model**: Tasks with `parent_task_id = null` are projects (root tasks)
- **Current Resources**: Task, User, Agent, System
- **User IDs**: Currently Firebase user IDs, migrating to internal user IDs
- **Missing**: Role-based permissions, hierarchical roles, permission overwrites, comprehensive UI

## 1. Database Schema Changes

### 1.1 New Tables to Create

#### Roles Table
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code (#FF0000)
    position INTEGER NOT NULL DEFAULT 0, -- Role hierarchy position
    icon_url TEXT, -- Optional role icon
    is_mentionable B<PERSON><PERSON><PERSON>N DEFAULT true,
    is_hoisted BOOLEAN DEFAULT false, -- Display separately in member list
    is_managed BOOLEAN DEFAULT false, -- System-managed role (cannot be deleted)
    created_by UUID NOT NULL REFERENCES users(id), -- Internal user ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### User_Roles Table (Many-to-Many)
```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- Internal user ID
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID NOT NULL REFERENCES users(id), -- Internal user ID who assigned the role
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);
```

#### Agent_Roles Table (Many-to-Many)
```sql
CREATE TABLE agent_roles (
    id UUID PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID NOT NULL REFERENCES users(id), -- Internal user ID who assigned the role
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, role_id)
);
```

#### Resource-Specific Permission Tables

##### Task Permissions Table
```sql
CREATE TABLE task_permissions (
    id UUID PRIMARY KEY,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE, -- NULL for global task permissions
    permissions BIGINT NOT NULL DEFAULT 0, -- TaskPermissions bitfield
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, task_id)
);
```

##### User Permissions Table
```sql
CREATE TABLE user_permissions (
    id UUID PRIMARY KEY,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    target_user_id UUID REFERENCES users(id) ON DELETE CASCADE, -- NULL for global user permissions
    permissions BIGINT NOT NULL DEFAULT 0, -- UserPermissions bitfield
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, target_user_id)
);
```

##### Agent Permissions Table
```sql
CREATE TABLE agent_permissions (
    id UUID PRIMARY KEY,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    target_agent_id UUID REFERENCES agents(id) ON DELETE CASCADE, -- NULL for global agent permissions
    permissions BIGINT NOT NULL DEFAULT 0, -- AgentPermissions bitfield
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, target_agent_id)
);
```

##### Role Permissions Table
```sql
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    target_role_id UUID REFERENCES roles(id) ON DELETE CASCADE, -- NULL for global role permissions
    permissions BIGINT NOT NULL DEFAULT 0, -- RolePermissions bitfield
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, target_role_id)
);
```

##### System Permissions Table
```sql
CREATE TABLE system_permissions (
    id UUID PRIMARY KEY,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    permissions BIGINT NOT NULL DEFAULT 0, -- SystemPermissions bitfield
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id) -- Only one system permission entry per role
);
```

#### Permission Overwrites Tables

##### Task Permission Overwrites
```sql
CREATE TABLE task_permission_overwrites (
    id UUID PRIMARY KEY,
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    target_type TEXT NOT NULL CHECK (target_type IN ('user', 'agent', 'role')),
    target_id TEXT NOT NULL, -- User ID, Agent ID, or Role ID
    allow_permissions BIGINT NOT NULL DEFAULT 0,
    deny_permissions BIGINT NOT NULL DEFAULT 0,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(task_id, target_type, target_id)
);
```

##### User Permission Overwrites
```sql
CREATE TABLE user_permission_overwrites (
    id UUID PRIMARY KEY,
    target_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subject_type TEXT NOT NULL CHECK (subject_type IN ('user', 'agent', 'role')),
    subject_id TEXT NOT NULL, -- Who is getting the permission
    allow_permissions BIGINT NOT NULL DEFAULT 0,
    deny_permissions BIGINT NOT NULL DEFAULT 0,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(target_user_id, subject_type, subject_id)
);
```

##### Agent Permission Overwrites
```sql
CREATE TABLE agent_permission_overwrites (
    id UUID PRIMARY KEY,
    target_agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    subject_type TEXT NOT NULL CHECK (subject_type IN ('user', 'agent', 'role')),
    subject_id TEXT NOT NULL, -- Who is getting the permission
    allow_permissions BIGINT NOT NULL DEFAULT 0,
    deny_permissions BIGINT NOT NULL DEFAULT 0,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(target_agent_id, subject_type, subject_id)
);
```

##### Role Permission Overwrites
```sql
CREATE TABLE role_permission_overwrites (
    id UUID PRIMARY KEY,
    target_role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    subject_type TEXT NOT NULL CHECK (subject_type IN ('user', 'agent', 'role')),
    subject_id TEXT NOT NULL, -- Who is getting the permission
    allow_permissions BIGINT NOT NULL DEFAULT 0,
    deny_permissions BIGINT NOT NULL DEFAULT 0,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(target_role_id, subject_type, subject_id)
);
```

##### System Permission Overwrites
```sql
CREATE TABLE system_permission_overwrites (
    id UUID PRIMARY KEY,
    target_type TEXT NOT NULL CHECK (target_type IN ('user', 'agent', 'role')),
    target_id TEXT NOT NULL, -- User ID, Agent ID, or Role ID
    allow_permissions BIGINT NOT NULL DEFAULT 0,
    deny_permissions BIGINT NOT NULL DEFAULT 0,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(target_type, target_id)
);
```

### 1.2 Default Roles to Create
- **@everyone** (position: 0, default role for all users)
- **Owner** (position: 1000, highest permissions)
- **Admin** (position: 900, administrative permissions)
- **Moderator** (position: 800, moderation permissions)
- **Member** (position: 100, basic member permissions)

## 2. Permission System Design

### 2.1 Permission Flags (Bitfield System per Resource Type)
Each resource type has its own 64-bit permission bitfield, allowing for resource-specific permissions:

```rust
// Task permissions (applies to both regular tasks and projects)
pub struct TaskPermissions;
impl TaskPermissions {
    pub const VIEW: u64 = 1 << 0;
    pub const CREATE: u64 = 1 << 1;
    pub const EDIT: u64 = 1 << 2;
    pub const DELETE: u64 = 1 << 3;
    pub const ASSIGN: u64 = 1 << 4;
    pub const MOVE: u64 = 1 << 5;
    pub const MANAGE_MEMBERS: u64 = 1 << 6; // For projects (root tasks)
    pub const VIEW_DEPENDENCIES: u64 = 1 << 7;
    pub const MANAGE_DEPENDENCIES: u64 = 1 << 8;
    pub const COMMENT: u64 = 1 << 9;
    pub const ATTACH_FILES: u64 = 1 << 10;
}

// User management permissions
pub struct UserPermissions;
impl UserPermissions {
    pub const VIEW: u64 = 1 << 0;
    pub const CREATE: u64 = 1 << 1;
    pub const EDIT: u64 = 1 << 2;
    pub const DELETE: u64 = 1 << 3;
    pub const ASSIGN_ROLES: u64 = 1 << 4;
    pub const REMOVE_ROLES: u64 = 1 << 5;
    pub const VIEW_PROFILE: u64 = 1 << 6;
    pub const EDIT_PROFILE: u64 = 1 << 7;
    pub const MANAGE_PERMISSIONS: u64 = 1 << 8;
}

// Agent management permissions
pub struct AgentPermissions;
impl AgentPermissions {
    pub const VIEW: u64 = 1 << 0;
    pub const CREATE: u64 = 1 << 1;
    pub const EDIT: u64 = 1 << 2;
    pub const DELETE: u64 = 1 << 3;
    pub const ASSIGN_ROLES: u64 = 1 << 4;
    pub const REMOVE_ROLES: u64 = 1 << 5;
    pub const CONFIGURE: u64 = 1 << 6;
    pub const ACTIVATE_DEACTIVATE: u64 = 1 << 7;
    pub const MANAGE_PERMISSIONS: u64 = 1 << 8;
}

// Role management permissions
pub struct RolePermissions;
impl RolePermissions {
    pub const VIEW: u64 = 1 << 0;
    pub const CREATE: u64 = 1 << 1;
    pub const EDIT: u64 = 1 << 2;
    pub const DELETE: u64 = 1 << 3;
    pub const ASSIGN: u64 = 1 << 4;
    pub const REMOVE: u64 = 1 << 5;
    pub const MANAGE_HIERARCHY: u64 = 1 << 6;
    pub const EDIT_PERMISSIONS: u64 = 1 << 7;
    pub const VIEW_MEMBERS: u64 = 1 << 8;
    pub const MANAGE_MEMBERS: u64 = 1 << 9;
}

// System-wide permissions
pub struct SystemPermissions;
impl SystemPermissions {
    pub const VIEW_SETTINGS: u64 = 1 << 0;
    pub const MANAGE_SETTINGS: u64 = 1 << 1;
    pub const VIEW_AUDIT_LOGS: u64 = 1 << 2;
    pub const MANAGE_AUDIT_LOGS: u64 = 1 << 3;
    pub const CREATE_PROJECTS: u64 = 1 << 4;
    pub const MANAGE_GLOBAL_PERMISSIONS: u64 = 1 << 5;
    pub const BACKUP_RESTORE: u64 = 1 << 6;
    pub const SYSTEM_MAINTENANCE: u64 = 1 << 7;
    pub const ADMINISTRATOR: u64 = 1 << 63; // Overrides all restrictions globally
}
```

### 2.2 Resource Types
The system supports permissions on the following resource types:

#### Primary Resources (things that need permission control):
- **`task`** - Both regular tasks and projects (projects are tasks with `parent_task_id = null`)
- **`system`** - Global system-level permissions (creating projects, managing users, etc.)

#### Secondary Resources (entities that can have permissions):
- **`user`** - User management permissions (view profile, edit user, delete user)
- **`agent`** - Agent management permissions (view agent, edit agent, delete agent)
- **`role`** - Role management permissions (create roles, assign roles, etc.)

#### Resource Permission Examples:
- **Task-specific**: User can edit Task A but not Task B
- **System-wide**: User can create new projects globally
- **User-specific**: User can edit User A's profile but not User B's
- **Role-specific**: User can assign Role X but not Role Y

### 2.3 How Separate Permission Tables Work

#### Permission Storage Architecture
- **Role Base Permissions**: Stored in separate tables per resource type (`task_permissions`, `user_permissions`, etc.)
- **Permission Overwrites**: Stored in separate overwrite tables per resource type
- **Extensibility**: Adding new resource types only requires new tables, no schema changes to existing tables
- **Performance**: Queries only touch relevant tables for the resource being checked

#### Permission Resolution Examples
```rust
// Checking if user can edit a specific task
let task_permissions = calculate_user_task_permissions(user_id, Some(task_id));
let can_edit = task_permissions & TaskPermissions::EDIT != 0;

// Checking if user can create users globally
let user_permissions = calculate_user_user_permissions(user_id, None);
let can_create = user_permissions & UserPermissions::CREATE != 0;

// Checking if user can assign a specific role
let role_permissions = calculate_user_role_permissions(user_id, Some(role_id));
let can_assign = role_permissions & RolePermissions::ASSIGN != 0;
```

#### Database Query Examples
```sql
-- Get all task permissions for a user's roles
SELECT tp.permissions
FROM task_permissions tp
JOIN user_roles ur ON tp.role_id = ur.role_id
WHERE ur.user_id = ? AND (tp.task_id = ? OR tp.task_id IS NULL);

-- Get task-specific overwrites
SELECT allow_permissions, deny_permissions
FROM task_permission_overwrites
WHERE task_id = ? AND target_type = 'user' AND target_id = ?;
```

### 2.4 Permission Resolution Algorithm
Following Discord's model, but applied per resource type:
1. Start with @everyone role permissions for the specific resource type
2. Apply all other role permissions for that resource type (bitwise OR)
3. Apply resource-specific overwrites for that resource type:
   - Apply role denies
   - Apply role allows
   - Apply user/agent denies
   - Apply user/agent allows
4. System Administrator permission bypasses all restrictions globally

## 3. Backend Implementation

### 3.1 New Rust Models

#### Core Models
- `Role` struct (simplified, no permission bitfields)
- `UserRole`, `AgentRole` junction models

#### Resource-Specific Permission Models
- `TaskPermission` - Role permissions for tasks
- `UserPermission` - Role permissions for users
- `AgentPermission` - Role permissions for agents
- `RolePermission` - Role permissions for roles
- `SystemPermission` - Role permissions for system

#### Permission Overwrite Models
- `TaskPermissionOverwrite` - Task-specific permission overwrites
- `UserPermissionOverwrite` - User-specific permission overwrites
- `AgentPermissionOverwrite` - Agent-specific permission overwrites
- `RolePermissionOverwrite` - Role-specific permission overwrites
- `SystemPermissionOverwrite` - System-specific permission overwrites

#### Services
- `PermissionCalculator` service with per-resource-type resolution
- `TaskPermissionService`, `UserPermissionService`, etc. for resource-specific operations
- Permission helper structs: `TaskPermissions`, `UserPermissions`, `AgentPermissions`, `RolePermissions`, `SystemPermissions`

### 3.2 New API Endpoints

#### Role Management
```
GET    /api/roles                    # List all roles
POST   /api/roles                    # Create role
GET    /api/roles/{id}               # Get role details
PUT    /api/roles/{id}               # Update role
DELETE /api/roles/{id}               # Delete role
PUT    /api/roles/{id}/position      # Update role position
```

#### Role Assignment
```
GET    /api/users/{id}/roles         # Get user's roles
POST   /api/users/{id}/roles         # Assign role to user
DELETE /api/users/{id}/roles/{role_id} # Remove role from user

GET    /api/agents/{id}/roles        # Get agent's roles
POST   /api/agents/{id}/roles        # Assign role to agent
DELETE /api/agents/{id}/roles/{role_id} # Remove role from agent
```

#### Resource-Specific Permissions
```
# Task Permissions
GET    /api/roles/{role_id}/permissions/tasks           # Get role's task permissions
PUT    /api/roles/{role_id}/permissions/tasks           # Set role's task permissions
GET    /api/roles/{role_id}/permissions/tasks/{task_id} # Get role's permissions for specific task
PUT    /api/roles/{role_id}/permissions/tasks/{task_id} # Set role's permissions for specific task

# User Permissions
GET    /api/roles/{role_id}/permissions/users           # Get role's user permissions
PUT    /api/roles/{role_id}/permissions/users           # Set role's user permissions
GET    /api/roles/{role_id}/permissions/users/{user_id} # Get role's permissions for specific user
PUT    /api/roles/{role_id}/permissions/users/{user_id} # Set role's permissions for specific user

# Agent Permissions
GET    /api/roles/{role_id}/permissions/agents           # Get role's agent permissions
PUT    /api/roles/{role_id}/permissions/agents           # Set role's agent permissions
GET    /api/roles/{role_id}/permissions/agents/{agent_id} # Get role's permissions for specific agent
PUT    /api/roles/{role_id}/permissions/agents/{agent_id} # Set role's permissions for specific agent

# Role Permissions
GET    /api/roles/{role_id}/permissions/roles           # Get role's role permissions
PUT    /api/roles/{role_id}/permissions/roles           # Set role's role permissions
GET    /api/roles/{role_id}/permissions/roles/{target_role_id} # Get role's permissions for specific role
PUT    /api/roles/{role_id}/permissions/roles/{target_role_id} # Set role's permissions for specific role

# System Permissions
GET    /api/roles/{role_id}/permissions/system          # Get role's system permissions
PUT    /api/roles/{role_id}/permissions/system          # Set role's system permissions
```

#### Permission Overwrites
```
# Task Overwrites
GET    /api/tasks/{task_id}/overwrites                  # Get task permission overwrites
POST   /api/tasks/{task_id}/overwrites                  # Create task permission overwrite
PUT    /api/tasks/{task_id}/overwrites/{target_type}/{target_id} # Update task permission overwrite
DELETE /api/tasks/{task_id}/overwrites/{target_type}/{target_id} # Delete task permission overwrite

# User Overwrites
GET    /api/users/{user_id}/overwrites                  # Get user permission overwrites
POST   /api/users/{user_id}/overwrites                  # Create user permission overwrite
PUT    /api/users/{user_id}/overwrites/{subject_type}/{subject_id} # Update user permission overwrite
DELETE /api/users/{user_id}/overwrites/{subject_type}/{subject_id} # Delete user permission overwrite

# Agent Overwrites
GET    /api/agents/{agent_id}/overwrites                # Get agent permission overwrites
POST   /api/agents/{agent_id}/overwrites                # Create agent permission overwrite
PUT    /api/agents/{agent_id}/overwrites/{subject_type}/{subject_id} # Update agent permission overwrite
DELETE /api/agents/{agent_id}/overwrites/{subject_type}/{subject_id} # Delete agent permission overwrite

# Role Overwrites
GET    /api/roles/{role_id}/overwrites                  # Get role permission overwrites
POST   /api/roles/{role_id}/overwrites                  # Create role permission overwrite
PUT    /api/roles/{role_id}/overwrites/{subject_type}/{subject_id} # Update role permission overwrite
DELETE /api/roles/{role_id}/overwrites/{subject_type}/{subject_id} # Delete role permission overwrite

# System Overwrites
GET    /api/system/overwrites                           # Get system permission overwrites
POST   /api/system/overwrites                           # Create system permission overwrite
PUT    /api/system/overwrites/{target_type}/{target_id} # Update system permission overwrite
DELETE /api/system/overwrites/{target_type}/{target_id} # Delete system permission overwrite
```

#### Permission Checking
```
GET    /api/permissions/check/{resource_type}/{resource_id}?permission={permission} # Check specific permission
POST   /api/permissions/bulk-check                      # Bulk permission checking
GET    /api/users/{user_id}/effective-permissions/{resource_type}/{resource_id} # Get effective permissions
```

### 3.3 Permission Middleware
- Authentication middleware to extract user/agent from token
- Permission checking middleware for route protection
- Context-aware permission resolution

## 4. Frontend Implementation

### 4.1 New Pages/Components

#### Roles Management Page (`/roles`)
- Role list with hierarchy visualization
- Create/edit role dialog
- Role permission matrix
- Member management per role
- Drag-and-drop role reordering

#### Permission Overwrites Component
- Resource-specific permission management
- Visual permission matrix (Allow/Deny/Inherit)
- Role and user/agent selection
- Real-time permission preview

#### Enhanced Contacts Page
- Role badges on user/agent cards
- Role assignment interface
- Permission summary display

### 4.2 UI Components to Create
- `RoleCard.vue` - Individual role display
- `RoleDialog.vue` - Create/edit role modal
- `PermissionMatrix.vue` - Permission grid interface
- `PermissionOverwriteDialog.vue` - Resource permission management
- `RoleBadge.vue` - Small role indicator
- `MemberRoleManager.vue` - Assign/remove roles interface

### 4.3 Stores to Create/Update
- `rolesStore.ts` - Role management state
- `permissionsStore.ts` - Permission calculation and caching
- Update `usersStore.ts` and `agentsStore.ts` for role integration

## 5. Implementation Phases

### Phase 1: Database and Core Models (Week 1)
- [ ] Create new database tables
- [ ] Implement Rust models and repositories
- [ ] Create default roles and permissions
- [ ] Basic permission calculation service

### Phase 2: Backend API (Week 2)
- [ ] Role CRUD endpoints
- [ ] Role assignment endpoints
- [ ] Permission overwrite endpoints
- [ ] Permission middleware and guards
- [ ] Migration scripts for existing data

### Phase 3: Frontend Core (Week 3)
- [ ] Role management page
- [ ] Basic role assignment interface
- [ ] Permission matrix component
- [ ] Update existing pages with role integration

### Phase 4: Advanced Features (Week 4)
- [ ] Permission overwrites UI
- [ ] Role hierarchy management
- [ ] Advanced permission visualization
- [ ] Audit logging for permission changes

### Phase 5: Testing and Polish (Week 5)
- [ ] Comprehensive testing
- [ ] Performance optimization
- [ ] UI/UX refinements
- [ ] Documentation updates

## 6. Key Features to Implement

### 6.1 Role Hierarchy
- Position-based role ordering
- Higher roles can manage lower roles
- Visual hierarchy in UI
- Drag-and-drop reordering

### 6.2 Permission Inheritance
- @everyone as base role for all users
- Multiple role assignment
- Permission accumulation (bitwise OR)
- Override system for specific resources

### 6.3 Visual Permission Management
- Color-coded permission states (Allow/Deny/Inherit)
- Permission templates for common roles
- Bulk permission assignment
- Permission conflict detection and warnings

### 6.4 Advanced Features
- Role mentioning in tasks/comments
- Role-based task assignment
- Permission audit logs
- Role templates and presets
- Temporary role assignments with expiration

## 7. Security Considerations

### 7.1 Permission Validation
- Server-side permission checks on all operations
- Prevent privilege escalation
- Role hierarchy enforcement
- Administrator permission safeguards

### 7.2 Audit Trail
- Log all permission changes
- Track role assignments/removals
- Monitor administrative actions
- Retention policies for audit logs

## 8. Migration Strategy

### 8.1 Data Migration
- Convert existing permissions to new role system
- Create default roles for existing users
- Preserve existing permission assignments
- Gradual rollout with feature flags

### 8.2 Backward Compatibility
- Maintain existing API endpoints during transition
- Dual permission checking (old + new system)
- Graceful degradation for unsupported features

### 8.3 User ID Migration
- Update all foreign key references from Firebase user IDs to internal user IDs
- Create mapping table for transition period
- Update authentication flow to use internal IDs
- Batch update existing data

## 9. Discord-Inspired UI Features

### 9.1 Role Display (Similar to Discord Screenshots)
- **Role List View**: Hierarchical list showing all roles with member counts
- **Role Colors**: Visual color coding for easy identification
- **Role Icons**: Optional icons for visual distinction
- **Member Count**: Display number of users/agents with each role

### 9.2 Role Management Interface
- **Role Creation Dialog**:
  - Name, description, color picker
  - Icon upload functionality
  - Position in hierarchy
  - Permission matrix with checkboxes
- **Role Editing**:
  - All creation features plus member management
  - Permission inheritance visualization
  - Conflict detection and warnings

### 9.3 Permission Matrix Interface
- **Grid Layout**: Permissions as rows, roles/users as columns
- **Three-State System**: Allow (green ✓), Deny (red ✗), Inherit (gray /)
- **Category Grouping**: Group related permissions together
- **Search and Filter**: Find specific permissions quickly
- **Bulk Operations**: Select multiple permissions for batch changes

### 9.4 Member Role Management
- **User Profile Integration**: Show roles on user profiles
- **Role Assignment Interface**:
  - Search and select roles to assign
  - Visual role hierarchy
  - Permission preview before assignment
- **Role Removal**: Easy removal with confirmation
- **Bulk Role Operations**: Assign/remove roles for multiple users

### 9.5 Resource Permission Overwrites
- **Channel-Style Overwrites**: Similar to Discord's channel permissions
- **Visual Override Indicators**: Show when permissions are overridden
- **Inheritance Chain**: Display how permissions are calculated
- **Override Management**: Easy add/edit/remove overwrites

## 10. Technical Implementation Details

### 10.1 Permission Calculation Performance
- **Caching Strategy**: Cache calculated permissions per user/resource
- **Invalidation**: Smart cache invalidation on role/permission changes
- **Batch Calculations**: Efficient bulk permission checking
- **Database Indexing**: Optimize queries for permission resolution

### 10.2 Real-time Updates
- **WebSocket Integration**: Real-time permission updates
- **Event Broadcasting**: Notify affected users of permission changes
- **UI Reactivity**: Immediate UI updates on permission changes

### 10.3 Error Handling and Validation
- **Permission Validation**: Prevent invalid permission combinations
- **Hierarchy Validation**: Ensure role hierarchy consistency
- **Circular Reference Prevention**: Avoid permission loops
- **Graceful Degradation**: Handle permission calculation failures

## Next Steps
1. **Review and Approval**: Stakeholder review of this comprehensive plan
2. **Environment Setup**: Prepare development environment for implementation
3. **Phase 1 Kickoff**: Begin database schema and core model implementation
4. **Regular Reviews**: Weekly progress reviews and plan adjustments
5. **User Testing**: Early user feedback on UI mockups and prototypes

This plan creates a Discord-like permission system that provides:
- **Hierarchical Role Management**: Clear role hierarchy with position-based ordering
- **Granular Permissions**: Fine-grained control over user capabilities
- **Visual Management Interface**: Intuitive UI for permission management
- **Resource-Specific Overwrites**: Channel-like permission overrides
- **Scalable Architecture**: Efficient permission calculation and caching
- **Security-First Design**: Comprehensive validation and audit trails

The implementation will transform the current basic permission system into a powerful, Discord-inspired role and permission management system that scales with organizational needs while maintaining ease of use.