# Discord-like Role and Permissions System Implementation Plan

## Overview
This plan outlines the implementation of a comprehensive role and permissions system similar to <PERSON>rd's, featuring hierarchical roles, granular permissions, channel-specific overwrites, and a complete management UI.

## Current System Analysis
- **Existing**: Basic permission system with Users, Agents, and Permissions tables
- **Current Permission Levels**: Read, Write, Admin (simple 3-level system)
- **Current Resources**: Task, Project, User, Agent, System
- **Missing**: Role-based permissions, hierarchical roles, permission overwrites, comprehensive UI

## 1. Database Schema Changes

### 1.1 New Tables to Create

#### Roles Table
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code (#FF0000)
    position INTEGER NOT NULL DEFAULT 0, -- Role hierarchy position
    icon_url TEXT, -- Optional role icon
    is_mentionable BOOLEAN DEFAULT true,
    is_hoisted BO<PERSON>EAN DEFAULT false, -- Display separately in member list
    is_managed BOOLEAN DEFAULT false, -- System-managed role (cannot be deleted)
    permissions BIGINT NOT NULL DEFAULT 0, -- Bitfield for base permissions
    created_by TEXT NOT NULL, -- Firebase user ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### User_Roles Table (Many-to-Many)
```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY,
    user_id TEXT NOT NULL, -- Firebase user ID
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by TEXT NOT NULL, -- Firebase user ID who assigned the role
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);
```

#### Agent_Roles Table (Many-to-Many)
```sql
CREATE TABLE agent_roles (
    id UUID PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by TEXT NOT NULL, -- Firebase user ID who assigned the role
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, role_id)
);
```

#### Permission_Overwrites Table
```sql
CREATE TABLE permission_overwrites (
    id UUID PRIMARY KEY,
    resource_type TEXT NOT NULL CHECK (resource_type IN ('task', 'project', 'channel', 'category')),
    resource_id TEXT NOT NULL, -- ID of the specific resource
    target_type TEXT NOT NULL CHECK (target_type IN ('user', 'agent', 'role')),
    target_id TEXT NOT NULL, -- User ID, Agent ID, or Role ID
    allow_permissions BIGINT NOT NULL DEFAULT 0, -- Explicitly allowed permissions
    deny_permissions BIGINT NOT NULL DEFAULT 0, -- Explicitly denied permissions
    created_by TEXT NOT NULL, -- Firebase user ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(resource_type, resource_id, target_type, target_id)
);
```

### 1.2 Default Roles to Create
- **@everyone** (position: 0, default role for all users)
- **Owner** (position: 1000, highest permissions)
- **Admin** (position: 900, administrative permissions)
- **Moderator** (position: 800, moderation permissions)
- **Member** (position: 100, basic member permissions)

## 2. Permission System Design

### 2.1 Permission Flags (Bitfield System)
```rust
// 64-bit permission flags
pub struct PermissionFlags;
impl PermissionFlags {
    // User management
    pub const VIEW_USERS: u64 = 1 << 1;
    pub const MANAGE_USERS: u64 = 1 << 2;
    pub const KICK_USERS: u64 = 1 << 3;
    pub const BAN_USERS: u64 = 1 << 4;

    // Role management
    pub const VIEW_ROLES: u64 = 1 << 5;
    pub const MANAGE_ROLES: u64 = 1 << 6;
    pub const ASSIGN_ROLES: u64 = 1 << 7;

    // Agent management
    pub const VIEW_AGENTS: u64 = 1 << 8;
    pub const MANAGE_AGENTS: u64 = 1 << 9;

    // Task permissions
    pub const VIEW_TASKS: u64 = 1 << 10;
    pub const CREATE_TASKS: u64 = 1 << 11;
    pub const EDIT_TASKS: u64 = 1 << 12;
    pub const DELETE_TASKS: u64 = 1 << 13;
    pub const ASSIGN_TASKS: u64 = 1 << 14;

    // System permissions
    pub const ADMINISTRATOR: u64 = 1 << 63; // Highest bit, overrides all
}
```

### 2.2 Permission Resolution Algorithm
Following Discord's model:
1. Start with @everyone role permissions
2. Apply all other role permissions (bitwise OR)
3. Apply resource-specific overwrites:
   - Apply role denies
   - Apply role allows
   - Apply user/agent denies
   - Apply user/agent allows
4. Administrator permission bypasses all restrictions

## 3. Backend Implementation

### 3.1 New Rust Models
- `Role` struct with permission bitfield
- `UserRole`, `AgentRole` junction models
- `PermissionOverwrite` model
- `PermissionCalculator` service for resolution

### 3.2 New API Endpoints
```
GET    /roles                    # List all roles
POST   /roles                    # Create role
GET    /roles/{id}               # Get role details
PUT    /roles/{id}               # Update role
DELETE /roles/{id}               # Delete role
PUT    /roles/{id}/position      # Update role position

GET    /users/{id}/roles         # Get user's roles
POST   /users/{id}/roles         # Assign role to user
DELETE /users/{id}/roles/{role_id} # Remove role from user

GET    /agents/{id}/roles        # Get agent's roles
POST   /agents/{id}/roles        # Assign role to agent
DELETE /agents/{id}/roles/{role_id} # Remove role from agent
GET    /permissions/{resource_type}/{resource_id} # Get overwrites
PUT    /permissions/{resource_type}/{resource_id} # Set overwrites
```

### 3.3 Permission Middleware
- Authentication middleware to extract user/agent from token
- Permission checking middleware for route protection
- Context-aware permission resolution

## 4. Frontend Implementation

### 4.1 New Pages/Components

#### Roles Management Page (`/roles`)
- Role list with hierarchy visualization
- Create/edit role dialog
- Role permission matrix
- Member management per role
- Drag-and-drop role reordering

#### Permission Overwrites Component
- Resource-specific permission management
- Visual permission matrix (Allow/Deny/Inherit)
- Role and user/agent selection
- Real-time permission preview

#### Enhanced Contacts Page
- Role badges on user/agent cards
- Role assignment interface
- Permission summary display

### 4.2 UI Components to Create
- `RoleCard.vue` - Individual role display
- `RoleDialog.vue` - Create/edit role modal
- `PermissionMatrix.vue` - Permission grid interface
- `PermissionOverwriteDialog.vue` - Resource permission management
- `RoleBadge.vue` - Small role indicator
- `MemberRoleManager.vue` - Assign/remove roles interface

### 4.3 Stores to Create/Update
- `rolesStore.ts` - Role management state
- `permissionsStore.ts` - Permission calculation and caching
- Update `usersStore.ts` and `agentsStore.ts` for role integration

## 5. Implementation Phases

### Phase 1: Database and Core Models (Week 1)
- [ ] Create new database tables
- [ ] Implement Rust models and repositories
- [ ] Create default roles and permissions
- [ ] Basic permission calculation service

### Phase 2: Backend API (Week 2)
- [ ] Role CRUD endpoints
- [ ] Role assignment endpoints
- [ ] Permission overwrite endpoints
- [ ] Permission middleware and guards
- [ ] Migration scripts for existing data

### Phase 3: Frontend Core (Week 3)
- [ ] Role management page
- [ ] Basic role assignment interface
- [ ] Permission matrix component
- [ ] Update existing pages with role integration

### Phase 4: Advanced Features (Week 4)
- [ ] Permission overwrites UI
- [ ] Role hierarchy management
- [ ] Advanced permission visualization
- [ ] Audit logging for permission changes

### Phase 5: Testing and Polish (Week 5)
- [ ] Comprehensive testing
- [ ] Performance optimization
- [ ] UI/UX refinements
- [ ] Documentation updates

## 6. Key Features to Implement

### 6.1 Role Hierarchy
- Position-based role ordering
- Higher roles can manage lower roles
- Visual hierarchy in UI
- Drag-and-drop reordering

### 6.2 Permission Inheritance
- @everyone as base role for all users
- Multiple role assignment
- Permission accumulation (bitwise OR)
- Override system for specific resources

### 6.3 Visual Permission Management
- Color-coded permission states (Allow/Deny/Inherit)
- Permission templates for common roles
- Bulk permission assignment
- Permission conflict detection and warnings

### 6.4 Advanced Features
- Role mentioning in tasks/comments
- Role-based task assignment
- Permission audit logs
- Role templates and presets
- Temporary role assignments with expiration

## 7. Security Considerations

### 7.1 Permission Validation
- Server-side permission checks on all operations
- Prevent privilege escalation
- Role hierarchy enforcement
- Administrator permission safeguards

### 7.2 Audit Trail
- Log all permission changes
- Track role assignments/removals
- Monitor administrative actions
- Retention policies for audit logs

## 8. Migration Strategy

### 8.1 Data Migration
- Create default roles for existing users
- Preserve existing permission assignments
- Gradual rollout with feature flags

### 8.2 Backward Compatibility
- Maintain existing API endpoints during transition
- Dual permission checking (old + new system)
- Graceful degradation for unsupported features

## 9. Discord-Inspired UI Features

### 9.1 Role Display (Similar to Discord Screenshots)
- **Role List View**: Hierarchical list showing all roles with member counts
- **Role Colors**: Visual color coding for easy identification
- **Role Icons**: Optional icons for visual distinction
- **Member Count**: Display number of users/agents with each role

### 9.2 Role Management Interface
- **Role Creation Dialog**:
  - Name, description, color picker
  - Icon upload functionality
  - Position in hierarchy
  - Permission matrix with checkboxes
- **Role Editing**:
  - All creation features plus member management
  - Permission inheritance visualization
  - Conflict detection and warnings

### 9.3 Permission Matrix Interface
- **Grid Layout**: Permissions as rows, roles/users as columns
- **Three-State System**: Allow (green ✓), Deny (red ✗), Inherit (gray /)
- **Category Grouping**: Group related permissions together
- **Search and Filter**: Find specific permissions quickly
- **Bulk Operations**: Select multiple permissions for batch changes

### 9.4 Member Role Management
- **User Profile Integration**: Show roles on user profiles
- **Role Assignment Interface**:
  - Search and select roles to assign
  - Visual role hierarchy
  - Permission preview before assignment
- **Role Removal**: Easy removal with confirmation
- **Bulk Role Operations**: Assign/remove roles for multiple users

### 9.5 Resource Permission Overwrites
- **Channel-Style Overwrites**: Similar to Discord's channel permissions
- **Visual Override Indicators**: Show when permissions are overridden
- **Inheritance Chain**: Display how permissions are calculated
- **Override Management**: Easy add/edit/remove overwrites

## 10. Technical Implementation Details

### 10.1 Permission Calculation Performance
- **Caching Strategy**: Cache calculated permissions per user/resource
- **Invalidation**: Smart cache invalidation on role/permission changes
- **Batch Calculations**: Efficient bulk permission checking
- **Database Indexing**: Optimize queries for permission resolution

### 10.2 Real-time Updates
- **WebSocket Integration**: Real-time permission updates
- **Event Broadcasting**: Notify affected users of permission changes
- **UI Reactivity**: Immediate UI updates on permission changes

### 10.3 Error Handling and Validation
- **Permission Validation**: Prevent invalid permission combinations
- **Hierarchy Validation**: Ensure role hierarchy consistency
- **Circular Reference Prevention**: Avoid permission loops
- **Graceful Degradation**: Handle permission calculation failures

## Next Steps
1. **Review and Approval**: Stakeholder review of this comprehensive plan
2. **Environment Setup**: Prepare development environment for implementation
3. **Phase 1 Kickoff**: Begin database schema and core model implementation
4. **Regular Reviews**: Weekly progress reviews and plan adjustments
5. **User Testing**: Early user feedback on UI mockups and prototypes

This plan creates a Discord-like permission system that provides:
- **Hierarchical Role Management**: Clear role hierarchy with position-based ordering
- **Granular Permissions**: Fine-grained control over user capabilities
- **Visual Management Interface**: Intuitive UI for permission management
- **Resource-Specific Overwrites**: Channel-like permission overrides
- **Scalable Architecture**: Efficient permission calculation and caching
- **Security-First Design**: Comprehensive validation and audit trails

The implementation will transform the current basic permission system into a powerful, Discord-inspired role and permission management system that scales with organizational needs while maintaining ease of use.